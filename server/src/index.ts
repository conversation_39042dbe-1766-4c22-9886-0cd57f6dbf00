import { Hono } from "hono";
import { cors } from "hono/cors";
import type { ApiResponse, ESealData, ESealResponse } from "shared/dist";

// Mock data untuk E-Seal
const mockESealData: ESealData[] = [
	{
		id: 1,
		perusahaan: "Perusahaan A",
		idVendor: "884GKEL637",
		merk: "BRAND A",
		model: "Model 2020",
		nomorIMEI: "875298572967967922",
		tipeESeal: "TIPE 123",
		token: "12356788",
		jarakTempuh: ">50 - 250 Km",
		status: "Unlocked"
	},
	{
		id: 2,
		perusahaan: "Perusahaan A",
		idVendor: "584GKEL637",
		merk: "BRAND B",
		model: "Model 2021",
		nomorIMEI: "252598572967967123",
		tipeESeal: "TIPE 456",
		token: "62466788",
		jarakTempuh: "<5 Km",
		status: "Locked"
	},
	{
		id: 3,
		perusahaan: "Perusahaan B",
		idVendor: "123GKEL789",
		merk: "BRAND C",
		model: "Model 2022",
		nomorIMEI: "987654321098765432",
		tipeESeal: "TIPE 789",
		token: "98765432",
		jarakTempuh: "250 - 500 Km",
		status: "Unlocked"
	},
	{
		id: 4,
		perusahaan: "Perusahaan C",
		idVendor: "456GKEL012",
		merk: "BRAND D",
		model: "Model 2023",
		nomorIMEI: "111222333444555666",
		tipeESeal: "TIPE 012",
		token: "11223344",
		jarakTempuh: ">500 Km",
		status: "Locked"
	},
	{
		id: 5,
		perusahaan: "Perusahaan D",
		idVendor: "789GKEL345",
		merk: "BRAND E",
		model: "Model 2024",
		nomorIMEI: "777888999000111222",
		tipeESeal: "TIPE 345",
		token: "77889900",
		jarakTempuh: "10 - 50 Km",
		status: "Unlocked"
	}
];

export const app = new Hono()

.use(cors())

.get("/", (c) => {
	return c.text("Hello Hono!");
})

.get("/hello", async (c) => {
	const data: ApiResponse = {
		message: "Hello BHVR!",
		success: true,
	};

	return c.json(data, { status: 200 });
})

.get("/api/eseal", async (c) => {
	const page = parseInt(c.req.query("page") || "1");
	const limit = parseInt(c.req.query("limit") || "10");
	const search = c.req.query("search") || "";

	let filteredData = mockESealData;

	if (search) {
		filteredData = mockESealData.filter(item =>
			item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
			item.idVendor.toLowerCase().includes(search.toLowerCase()) ||
			item.merk.toLowerCase().includes(search.toLowerCase()) ||
			item.model.toLowerCase().includes(search.toLowerCase()) ||
			item.nomorIMEI.includes(search) ||
			item.tipeESeal.toLowerCase().includes(search.toLowerCase()) ||
			item.token.includes(search)
		);
	}

	const startIndex = (page - 1) * limit;
	const endIndex = startIndex + limit;
	const paginatedData = filteredData.slice(startIndex, endIndex);

	const response: ESealResponse = {
		data: paginatedData,
		total: filteredData.length,
		page,
		limit
	};

	return c.json(response, { status: 200 });
});

export default app;