import { useState } from "react";
import { Button } from "../../../components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function TrackingStop() {
  const [search, setSearch] = useState("");
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data untuk table Stop
  const mockData = [
    {
      id: 1,
      perusahaan: "Perusahaan A",
      alamatStop: "Jakarta Utara, DKI Jakarta",
      idVendor: "884GKEL637",
      latitudeStop: "-13156771",
      longitudeStop: "7748481.11",
      noIMEI: "875298572967967922",
      nomorESeal: "784858252",
      token: "12356788",
    },
  ];

  const filteredData = mockData.filter(
    (item) =>
      item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
      item.alamatStop.toLowerCase().includes(search.toLowerCase()) ||
      item.idVendor.toLowerCase().includes(search.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Stop</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(parseInt(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <input
              type="text"
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border border-gray-300 rounded px-3 py-1 pl-8 text-sm w-64"
            />
            <svg
              className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[50px] font-semibold text-slate-700 text-center p-2">
                  No
                </th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">
                  Perusahaan
                </th>
                <th className="min-w-[200px] font-semibold text-slate-700 text-center p-2">
                  Alamat Stop
                </th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">
                  ID Vendor
                </th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">
                  Latitude Stop
                </th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">
                  Longitude Stop
                </th>
                <th className="min-w-[180px] font-semibold text-slate-700 text-center p-2">
                  No IMEI
                </th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">
                  Nomor E-Seal
                </th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">
                  Token
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan={9} className="text-center py-8 text-gray-500">
                    {search ? "Data tidak ditemukan" : "Data Masih Kosong"}
                  </td>
                </tr>
              ) : (
                filteredData.map((item, index) => (
                  <tr
                    key={item.id}
                    className="hover:bg-slate-50 transition-colors border-b"
                  >
                    <td className="text-center p-2 font-medium">
                      {startEntry + index}.
                    </td>
                    <td className="text-center p-2">{item.perusahaan}</td>
                    <td className="text-center p-2">{item.alamatStop}</td>
                    <td className="text-center p-2 font-mono text-sm">
                      {item.idVendor}
                    </td>
                    <td className="text-center p-2 font-mono text-sm">
                      {item.latitudeStop}
                    </td>
                    <td className="text-center p-2 font-mono text-sm">
                      {item.longitudeStop}
                    </td>
                    <td className="text-center p-2 font-mono text-sm">
                      {item.noIMEI}
                    </td>
                    <td className="text-center p-2 font-mono text-sm">
                      {item.nomorESeal}
                    </td>
                    <td className="text-center p-2 font-mono text-sm">
                      {item.token}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            ))}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}