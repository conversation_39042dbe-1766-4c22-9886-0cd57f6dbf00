import { useState } from 'react';

export default function TrackingStatus() {
  const [idVendor, setIdVendor] = useState('');
  const [nomorESeal, setNomorESeal] = useState('');
  const [token, setToken] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', { idVendor, nomorESeal, token });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Status</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <input
              type="text"
              placeholder="ID Vendor *"
              value={idVendor}
              onChange={(e) => setIdVendor(e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              required
            />
          </div>
          <div>
            <input
              type="text"
              placeholder="Nomor E-Seal *"
              value={nomorESeal}
              onChange={(e) => setNomorESeal(e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              required
            />
          </div>
        </div>

        <div>
          <input
            type="text"
            placeholder="Token *"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            required
          />
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">* Kolom wajib diisi</span>
          <button
            type="submit"
            className="bg-blue-600 text-white px-6 py-2 rounded text-sm hover:bg-blue-700 transition-colors"
          >
            Cari
          </button>
        </div>
      </form>

      {/* Result Table */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900">Nomor E-Seal: -</h2>

        <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-3">Perusahaan</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-3">Start</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-3">Update Position</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-3">Stop</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4} className="text-center py-8 text-gray-500">
                  Data Masih Kosong
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

