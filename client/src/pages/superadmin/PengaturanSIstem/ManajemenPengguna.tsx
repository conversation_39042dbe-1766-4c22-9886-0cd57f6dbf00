import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Button } from '../../../components/ui/button';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
}

const Modal = ({ isOpen, onClose, onConfirm, username }: { isOpen: boolean; onClose: () => void; onConfirm: () => void; username: string }) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/40 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="mb-4">
          <h3 className="text-lg font-semibold">Kon<PERSON>rmasi Ha<PERSON></h3>
          <p className="text-sm text-gray-600">
            <PERSON><PERSON><PERSON><PERSON><PERSON> yakin ingin menghapus pengguna {username}? Aksi ini akan menghapus semua data pengguna.
          </p>
        </div>
        <div className="flex justify-end space-x-2">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            onClick={onClose}
          >
            Batal
          </button>
          <button
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            onClick={onConfirm}
          >
            Hapus
          </button>
        </div>
      </div>
    </div>
  );
};

export default function ManajemenPengguna() {
  const [users] = useState<User[]>([
    { id: 1, username: 'user1', email: '<EMAIL>', role: 'Admin' },
    { id: 2, username: 'user2', email: '<EMAIL>', role: 'Operator' },
  ]);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  const handleDeleteConfirm = (userId: number) => {
    // Add your delete logic here (e.g., API call)
    console.log(`Deleting user with ID: ${userId}`);
    setSelectedUserId(null);
  };

  return (
    <div className="space-y-6 p-4">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Manajemen Pengguna</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 border-b-2 border-slate-200">
              <TableHead className="min-w-[50px] font-semibold text-slate-700">No</TableHead>
              <TableHead className="min-w-[150px] font-semibold text-slate-700">Username</TableHead>
              <TableHead className="min-w-[200px] font-semibold text-slate-700">Email</TableHead>
              <TableHead className="min-w-[120px] font-semibold text-slate-700">Role</TableHead>
              <TableHead className="min-w-[100px] font-semibold text-slate-700">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user, index) => (
              <TableRow key={user.id} className="hover:bg-slate-50 transition-colors">
                <TableCell className="font-medium">{index + 1}.</TableCell>
                <TableCell>{user.username}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => setSelectedUserId(user.id)}
                  >
                    Hapus
                  </Button>
                  <Modal
                    isOpen={selectedUserId === user.id}
                    onClose={() => setSelectedUserId(null)}
                    onConfirm={() => handleDeleteConfirm(user.id)}
                    username={user.username}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}