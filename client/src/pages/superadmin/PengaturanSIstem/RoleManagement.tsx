import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Button } from '../../../components/ui/button';

interface Role {
  id: number;
  name: string;
  permissions: string;
}

export default function RoleManagement() {
  const [roles] = useState<Role[]>([
    { id: 1, name: 'Admin', permissions: 'Full Access' },
    { id: 2, name: 'Operator', permissions: 'Read Only' },
  ]);
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null);

  const handleDeleteConfirm = (roleId: number) => {
    // Add your delete logic here (e.g., API call)
    console.log(`Deleting role with ID: ${roleId}`);
    setSelectedRoleId(null);
  };

  return (
    <div className="space-y-6 p-4">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Role Management</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 border-b-2 border-slate-200">
              <TableHead className="min-w-[50px] font-semibold text-slate-700">No</TableHead>
              <TableHead className="min-w-[150px] font-semibold text-slate-700">Role Name</TableHead>
              <TableHead className="min-w-[200px] font-semibold text-slate-700">Permissions</TableHead>
              <TableHead className="min-w-[100px] font-semibold text-slate-700">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {roles.map((role, index) => (
              <TableRow key={role.id} className="hover:bg-slate-50 transition-colors">
                <TableCell className="font-medium">{index + 1}.</TableCell>
                <TableCell>{role.name}</TableCell>
                <TableCell>{role.permissions}</TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => setSelectedRoleId(role.id)}
                  >
                    Hapus
                  </Button>
                  {selectedRoleId === role.id && (
                    <div
                      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                      onClick={() => setSelectedRoleId(null)}
                    >
                      <div
                        className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="mb-4">
                          <h3 className="text-lg font-semibold">Konfirmasi Hapus</h3>
                          <p className="text-sm text-gray-600">
                            Apakah Anda yakin ingin menghapus peran {role.name}? Aksi ini akan menghapus semua izin terkait.
                          </p>
                        </div>
                        <div className="flex justify-end space-x-2">
                          <button
                            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                            onClick={() => setSelectedRoleId(null)}
                          >
                            Batal
                          </button>
                          <button
                            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                            onClick={() => handleDeleteConfirm(role.id)}
                          >
                            Hapus
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}