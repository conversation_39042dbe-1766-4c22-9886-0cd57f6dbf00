import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import { Search, ChevronLeft, ChevronRight } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';

interface LogData {
  id: number;
  timestamp: string;
  event: string;
  deviceId: string;
  details: string;
  user: string;
}

const mockLogData: LogData[] = [
  {
    id: 1,
    timestamp: "2025-07-17 12:30:00",
    event: "Device Activated",
    deviceId: "884GKEL637",
    details: "Activation successful",
    user: "admin_user"
  },
  {
    id: 2,
    timestamp: "2025-07-17 11:45:00",
    event: "Position Updated",
    deviceId: "464641LU3",
    details: "Latitude: 6427373737, Longitude: 2734636364",
    user: "operator_01"
  }
];

export default function Logs() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const filteredData = mockLogData.filter(item =>
    item.timestamp.toLowerCase().includes(search.toLowerCase()) ||
    item.event.toLowerCase().includes(search.toLowerCase()) ||
    item.deviceId.toLowerCase().includes(search.toLowerCase()) ||
    item.details.toLowerCase().includes(search.toLowerCase()) ||
    item.user.toLowerCase().includes(search.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Logs</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
        <p className="text-gray-600 mt-2">Pantau riwayat aktivitas dan event sistem</p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <Select value={entriesPerPage.toString()} onValueChange={(value) => {
            setEntriesPerPage(parseInt(value));
            setCurrentPage(1);
          }}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-64 sm:w-48 md:w-64"
            />
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-50 border-b-2 border-slate-200">
                <TableHead className="min-w-[50px] font-semibold text-slate-700 text-center">No</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Timestamp</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Event</TableHead>
                <TableHead className="min-w-[100px] font-semibold text-slate-700 text-center">Device ID</TableHead>
                <TableHead className="min-w-[200px] font-semibold text-slate-700 text-center">Details</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">User</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data Masih Kosong'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredData.slice((currentPage - 1) * entriesPerPage, currentPage * entriesPerPage).map((item, index) => (
                  <TableRow key={item.id} className="hover:bg-slate-50 transition-colors">
                    <TableCell className="text-center font-medium">{startEntry + index}.</TableCell>
                    <TableCell className="text-center">{item.timestamp}</TableCell>
                    <TableCell className="text-center">{item.event}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.deviceId}</TableCell>
                    <TableCell className="text-center">{item.details}</TableCell>
                    <TableCell className="text-center">{item.user}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            ))}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}