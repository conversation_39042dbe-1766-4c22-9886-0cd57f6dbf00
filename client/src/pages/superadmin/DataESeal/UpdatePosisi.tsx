import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';

export default function UpdatePosisi() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data sesuai dengan struktur table yang diminta
  const mockData = [
    {
      id: 1,
      perusahaan: "Perusahaan A",
      address: "Jalan Pantura, Kendal, Jawa Tengah",
      posisiAltitude: "6363737373737",
      dayaBaterai: "15000",
      dayaAki: "350000",
      event: "UNLOCK",
      idVendor: "464641LU3",
      kecepatan: "0",
      posisiLatitude: "6427373737",
      posisiLongitude: "2734636364",
      provinsi: "Jawa Tengah",
      suhu: "Rendah",
      noIMEI: "862048059607602",
      noESeal: "3M8M1S5"
    }
  ];

  const filteredData = mockData.filter(item =>
    item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
    item.address.toLowerCase().includes(search.toLowerCase()) ||
    item.event.toLowerCase().includes(search.toLowerCase()) ||
    item.idVendor.toLowerCase().includes(search.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Update Posisi</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(parseInt(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <input
              type="text"
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border border-gray-300 rounded px-3 py-1 pl-8 text-sm w-64"
            />
            <svg className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[50px] font-semibold text-slate-700 text-center p-2">No</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Perusahaan</th>
                <th className="min-w-[200px] font-semibold text-slate-700 text-center p-2">Address</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Posisi Altitude</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Daya Baterai</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Daya Aki</th>
                <th className="min-w-[80px] font-semibold text-slate-700 text-center p-2">Event</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">ID Vendor</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Kecepatan Kontainer</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Posisi Latitude</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Posisi Longitude</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Provinsi</th>
                <th className="min-w-[80px] font-semibold text-slate-700 text-center p-2">Suhu</th>
                <th className="min-w-[150px] font-semibold text-slate-700 text-center p-2">No IMEI</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">No E-Seal</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan={15} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data Masih Kosong'}
                  </td>
                </tr>
              ) : (
                filteredData.map((item, index) => (
                  <tr key={item.id} className="hover:bg-slate-50 transition-colors border-b">
                    <td className="text-center p-2 font-medium">{startEntry + index}.</td>
                    <td className="text-center p-2">{item.perusahaan}</td>
                    <td className="text-center p-2">{item.address}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.posisiAltitude}</td>
                    <td className="text-center p-2">{item.dayaBaterai}</td>
                    <td className="text-center p-2">{item.dayaAki}</td>
                    <td className="text-center p-2">
                      <span className={`px-2 py-1 rounded text_xs font-medium ${
                        item.event === 'UNLOCK'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {item.event}
                      </span>
                    </td>
                    <td className="text-center p-2 font-mono text-sm">{item.idVendor}</td>
                    <td className="text-center p-2">{item.kecepatan}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.posisiLatitude}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.posisiLongitude}</td>
                    <td className="text-center p-2">{item.provinsi}</td>
                    <td className="text-center p-2">{item.suhu}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.noIMEI}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.noESeal}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            ))}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}