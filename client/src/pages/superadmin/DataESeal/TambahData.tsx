import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { hcWithType } from 'server/dist/client';
import type { ESealData, ESealResponse } from 'shared/dist';

const SERVER_URL = import.meta.env.VITE_SERVER_URL || "http://localhost:3000";
const client = hcWithType(SERVER_URL);

export default function TambahData() {
  const [data, setData] = useState<ESealData[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [totalEntries, setTotalEntries] = useState(0);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await client.api.eseal.$get({
        query: {
          page: currentPage.toString(),
          limit: entriesPerPage.toString(),
          search: search
        }
      });
      
      if (response.ok) {
        const result: ESealResponse = await response.json();
        setData(result.data);
        setTotalEntries(result.total);
        setTotalPages(Math.ceil(result.total / entriesPerPage));
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [currentPage, entriesPerPage, search]);

  const handleSearch = (value: string) => {
    setSearch(value);
    setCurrentPage(1);
  };

  const getStatusBadge = (status: string) => {
    return (
      <Badge 
        variant={status === 'Unlocked' ? 'destructive' : 'secondary'}
        className={
          status === 'Unlocked' 
            ? 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200' 
            : 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200'
        }
      >
        {status}
      </Badge>
    );
  };

  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tambah Data E-Seal</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
        <p className="text-gray-600 mt-2">Kelola dan pantau data E-Seal untuk setiap pengiriman</p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <Select value={entriesPerPage.toString()} onValueChange={(value) => {
            setEntriesPerPage(parseInt(value));
            setCurrentPage(1);
          }}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search..."
              value={search}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 w-64 sm:w-48 md:w-64"
            />
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-50 border-b-2 border-slate-200">
                <TableHead className="w-12 min-w-[50px] font-semibold text-slate-700">No</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700">Perusahaan</TableHead>
                <TableHead className="min-w-[100px] font-semibold text-slate-700">ID Vendor</TableHead>
                <TableHead className="min-w-[80px] font-semibold text-slate-700">Merk</TableHead>
                <TableHead className="min-w-[100px] font-semibold text-slate-700">Model</TableHead>
                <TableHead className="min-w-[150px] font-semibold text-slate-700">Nomor IMEI</TableHead>
                <TableHead className="min-w-[100px] font-semibold text-slate-700">Tipe E-Seal</TableHead>
                <TableHead className="min-w-[80px] font-semibold text-slate-700">Token</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700">Jarak Tempuh</TableHead>
                <TableHead className="min-w-[80px] font-semibold text-slate-700">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : data.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    No data available
                  </TableCell>
                </TableRow>
              ) : (
                data.map((item, index) => (
                  <TableRow key={item.id} className="hover:bg-slate-50 transition-colors">
                    <TableCell className="font-medium">{startEntry + index}.</TableCell>
                    <TableCell className="font-medium">{item.perusahaan}</TableCell>
                    <TableCell>{item.idVendor}</TableCell>
                    <TableCell>{item.merk}</TableCell>
                    <TableCell>{item.model}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorIMEI}</TableCell>
                    <TableCell>{item.tipeESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.token}</TableCell>
                    <TableCell>{item.jarakTempuh}</TableCell>
                    <TableCell>{getStatusBadge(item.status)}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            ))}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}