/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
// import { Badge } from '../../../components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search } from 'lucide-react';

interface DokumenData {
  id: number;
  nomorAju: string;
  perusahaan: string;
  kodeDokumen: string;
  nomorDaftar: string;
  tanggalDaftar: string;
  kodeKantor: string;
  namaKantor: string;
  kodeTps: string;
  namaGudang: string;
  idPerusahaan: string;
  namaPerusahaan: string;
  uraian: string;
  nomorKontainer: string;
  nomorSegel: string;
}

const mockDokumenData: DokumenData[] = [
  {
    id: 1,
    nomorAju: "222",
    perusahaan: "Perusahaan A",
    kodeDokumen: "263722",
    nomorDaftar: "BB4627",
    tanggalDaftar: "2025-01-25",
    kodeKantor: "55",
    namaKantor: "Kantor ABC",
    kodeTps: "10",
    namaGudang: "Gudang Utara",
    idPerusahaan: "4448C1015",
    namaPerusahaan: "John Doe",
    uraian: "Briket Grade A 3 ton",
    nomorKontainer: "8944H546",
    nomorSegel: "8944H546"
  }
];

export default function DokumenKepabeaan() {
  const [search, setSearch] = useState('');
  const [data] = useState<DokumenData[]>(mockDokumenData);

  const filteredData = data.filter(item =>
    item.nomorAju.toLowerCase().includes(search.toLowerCase()) ||
    item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
    item.kodeDokumen.toLowerCase().includes(search.toLowerCase()) ||
    item.nomorDaftar.toLowerCase().includes(search.toLowerCase()) ||
    item.namaPerusahaan.toLowerCase().includes(search.toLowerCase()) ||
    item.uraian.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Dokumen Kepabeaan</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
        <p className="text-gray-600 mt-2">Kelola dan pantau dokumen kepabeaan untuk setiap pengiriman</p>
      </div>

      {/* Search and Actions */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Masukkan Nomor Aju</span>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Nomor Aju..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-64 sm:w-48 md:w-64"
            />
          </div>
        </div>

        <Button className="bg-blue-600 hover:bg-blue-700">
          <Search className="w-4 h-4 mr-2" />
          Cari
        </Button>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-50 border-b-2 border-slate-200">
                <TableHead className="min-w-[80px] font-semibold text-slate-700 text-center">Nomor Aju</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Perusahaan</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Kode Dokumen</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Nomor Daftar</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Tanggal Daftar</TableHead>
                <TableHead className="min-w-[100px] font-semibold text-slate-700 text-center">Kode Kantor</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Nama Kantor</TableHead>
                <TableHead className="min-w-[80px] font-semibold text-slate-700 text-center">Kode TPS</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Nama Gudang</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">ID Perusahaan</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Nama Perusahaan</TableHead>
                <TableHead className="min-w-[150px] font-semibold text-slate-700 text-center">Uraian</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Nomor Kontainer</TableHead>
                <TableHead className="min-w-[120px] font-semibold text-slate-700 text-center">Nomor Segel</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={14} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data Masih Kosong'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredData.map((item, index) => (
                  <TableRow key={item.id} className="hover:bg-slate-50 transition-colors">
                    <TableCell className="text-center font-mono text-sm">{item.nomorAju}</TableCell>
                    <TableCell className="text-center">{item.perusahaan}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.kodeDokumen}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.nomorDaftar}</TableCell>
                    <TableCell className="text-center">{item.tanggalDaftar}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.kodeKantor}</TableCell>
                    <TableCell className="text-center">{item.namaKantor}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.kodeTps}</TableCell>
                    <TableCell className="text-center">{item.namaGudang}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.idPerusahaan}</TableCell>
                    <TableCell className="text-center">{item.namaPerusahaan}</TableCell>
                    <TableCell className="text-center">{item.uraian}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.nomorKontainer}</TableCell>
                    <TableCell className="text-center font-mono text-sm">{item.nomorSegel}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>


    </div>
  );
}
