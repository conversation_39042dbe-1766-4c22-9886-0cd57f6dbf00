import { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, User } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';

interface SidebarProps {
  activeMenu: string;
  onMenuClick: (menu: string) => void;
  isMobile?: boolean;
}

export function Sidebar({ activeMenu, onMenuClick, isMobile = false }: SidebarProps) {
  const navigate = useNavigate();
  const [isDataESealOpen, setIsDataESealOpen] = useState(false);
  const [isTrackingOpen, setIsTrackingOpen] = useState(false);
  const [isPengaturanOpen, setIsPengaturanOpen] = useState(false);

  const menuItems = [
    {
      id: 'tambah-data',
      label: 'Tambah Data',
      parent: 'data-eseal',
      path: '/superadmin/tambah-data'
    },
    {
      id: 'dokumen-kepabeaan',
      label: 'Doku<PERSON>',
      parent: 'data-eseal',
      path: '/superadmin/dokumen-kepabeaan'
    },
    {
      id: 'update-posisi',
      label: 'Update Posisi',
      parent: 'data-eseal',
      path: '/superadmin/update-posisi'
    },
    {
      id: 'update-status-device',
      label: 'Update Status Device',
      parent: 'data-eseal',
      path: '/superadmin/update-status-device'
    },
    {
      id: 'tracking-start',
      label: 'Start',
      parent: 'tracking-data',
      path: '/superadmin/tracking-start'
    },
    {
      id: 'tracking-stop',
      label: 'Stop',
      parent: 'tracking-data',
      path: '/superadmin/tracking-stop'
    },
    {
      id: 'tracking-status',
      label: 'Status',
      parent: 'tracking-data',
      path: '/superadmin/tracking-status'
    },
    {
      id: 'logs',
      label: 'Logs',
      parent: 'logs',
      path: '/superadmin/logs'
    },
    {
      id: 'manajemen-pengguna',
      label: 'Manajemen Pengguna',
      parent: 'pengaturan-sistem',
      path: '/superadmin/manajemen-pengguna'
    },
    {
      id: 'role-management',
      label: 'Role Management',
      parent: 'pengaturan-sistem',
      path: '/superadmin/role-management'
    }
  ];

  // Sync the open state with the active menu
  useEffect(() => {
    setIsDataESealOpen(menuItems.some(item => item.parent === 'data-eseal' && item.id === activeMenu));
    setIsTrackingOpen(menuItems.some(item => item.parent === 'tracking-data' && item.id === activeMenu));
    setIsPengaturanOpen(menuItems.some(item => item.parent === 'pengaturan-sistem' && item.id === activeMenu));
  }, [activeMenu]);

  const sidebarClasses = isMobile
    ? 'w-full text-white h-full block'
    : 'w-64 bg-slate-800 text-white rounded-xl shadow-lg p-4 h-full flex flex-col';

  return (
    <div className={sidebarClasses}>
      {/* Header Content for Mobile */}
      {isMobile && (
        <div className="mb-6 pb-4 border-b border-slate-600">
          {/* Logo */}
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center shadow-md">
              <span className="text-white font-bold text-sm">VIT</span>
            </div>
            <div>
              <div className="text-sm font-medium text-white">VIT-PLI</div>
              <div className="text-xs text-slate-300">Vessel Tracking & Monitoring</div>
            </div>
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 text-white hover:bg-slate-700 w-full justify-start">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-gray-600" />
                </div>
                <span className="text-sm font-medium">Super Admin</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuItem>Profile</DropdownMenuItem>
              <DropdownMenuItem>Settings</DropdownMenuItem>
              <DropdownMenuItem>Logout</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      <div className="flex-1 overflow-y-auto">
        {/* Data E-Seal Section */}
        <div className="mb-4">
          <button
            onClick={() => setIsDataESealOpen(!isDataESealOpen)}
            className="flex items-center justify-between w-full p-3 bg-slate-700 rounded-lg mb-2 hover:bg-slate-600 transition-colors"
          >
            <span className="font-medium">Data E-Seal</span>
            {isDataESealOpen ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
          {isDataESealOpen && (
            <div className="ml-4 space-y-1">
              {menuItems.filter(item => item.parent === 'data-eseal').map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    onMenuClick(item.id);
                    navigate(item.path);
                  }}
                  className={`w-full text-left p-2 rounded transition-colors ${
                    activeMenu === item.id
                      ? 'bg-amber-100 text-amber-900 shadow-md border border-amber-200'
                      : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                  }`}
                >
                  {item.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Tracking Data Section */}
        <div className="mb-4">
          <button
            onClick={() => setIsTrackingOpen(!isTrackingOpen)}
            className="flex items-center justify-between w-full p-3 bg-slate-700 rounded-lg mb-2 hover:bg-slate-600 transition-colors"
          >
            <span className="font-medium">Tracking Data</span>
            {isTrackingOpen ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
          {isTrackingOpen && (
            <div className="ml-4 space-y-1">
              {menuItems.filter(item => item.parent === 'tracking-data').map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    onMenuClick(item.id);
                    navigate(item.path);
                  }}
                  className={`w-full text-left p-2 rounded transition-colors ${
                    activeMenu === item.id
                      ? 'bg-amber-100 text-amber-900 shadow-md border border-amber-200'
                      : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                  }`}
                >
                  {item.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Logs Section */}
        <div>
          <button
            onClick={() => {
              onMenuClick('logs');
              navigate('/superadmin/logs');
            }}
            className={`w-full text-left p-3 rounded-lg transition-colors ${
              activeMenu === 'logs'
                ? 'bg-amber-100 text-amber-900 shadow-md border border-amber-200'
                : 'text-slate-300 hover:bg-slate-700 hover:text-white'
            }`}
          >
            Logs
          </button>
        </div>

        {/* Pengaturan Sistem Section */}
        <div className="mb-4">
          <button
            onClick={() => setIsPengaturanOpen(!isPengaturanOpen)}
            className="flex items-center justify-between w-full p-3 bg-slate-700 rounded-lg mb-2 hover:bg-slate-600 transition-colors"
          >
            <span className="font-medium">Pengaturan Sistem</span>
            {isPengaturanOpen ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
          {isPengaturanOpen && (
            <div className="ml-4 space-y-1">
              {menuItems.filter(item => item.parent === 'pengaturan-sistem').map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    onMenuClick(item.id);
                    navigate(item.path);
                  }}
                  className={`w-full text-left p-2 rounded transition-colors ${
                    activeMenu === item.id
                      ? 'bg-amber-100 text-amber-900 shadow-md border border-amber-200'
                      : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                  }`}
                >
                  {item.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}