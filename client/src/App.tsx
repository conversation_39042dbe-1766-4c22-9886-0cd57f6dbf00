import { useEffect, useState } from "react";
import { Routes, Route, useNavigate, useLocation } from "react-router-dom";
import { Layout } from "./components/Layout";
import TambahData from "./pages/superadmin/DataESeal/TambahData";
import DokumenKepabeaan from "./pages/superadmin/DataESeal/DokumenKepabeaan";
import UpdatePosisi from "./pages/superadmin/DataESeal/UpdatePosisi";
import UpdateStatusDevice from "./pages/superadmin/DataESeal/UpdateStatusDevice";
import TrackingStart from "./pages/superadmin/TrackingData/TrackingStart";
import TrackingStop from "./pages/superadmin/TrackingData/TrackingStop";
import TrackingStatus from "./pages/superadmin/TrackingData/TrackingStatus";
import Logs from "./pages/superadmin/Logs/Logs";
import ManajemenPengguna from "./pages/superadmin/PengaturanSIstem/ManajemenPengguna";
import RoleManagement from "./pages/superadmin/PengaturanSIstem/RoleManagement";

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeMenu, setActiveMenu] = useState("tambah-data");

  // Map paths to menu IDs
  const pathToMenuId = {
    "/": "tambah-data",
    "/superadmin/tambah-data": "tambah-data",
    "/superadmin/dokumen-kepabeaan": "dokumen-kepabeaan",
    "/superadmin/update-posisi": "update-posisi",
    "/superadmin/update-status-device": "update-status-device",
    "/superadmin/tracking-start": "tracking-start",
    "/superadmin/tracking-stop": "tracking-stop",
    "/superadmin/tracking-status": "tracking-status",
    "/superadmin/logs": "logs",
    "/superadmin/manajemen-pengguna": "manajemen-pengguna",
    "/superadmin/role-management": "role-management",
  };

  // Update active menu based on current path
  useEffect(() => {
    const menuId =
      pathToMenuId[location.pathname as keyof typeof pathToMenuId] ||
      "tambah-data";
    setActiveMenu(menuId);
  }, [location.pathname]);

  const handleMenuClick = (menuId: string) => {
    const menuToPath = {
      "tambah-data": "/superadmin/tambah-data",
      "dokumen-kepabeaan": "/superadmin/dokumen-kepabeaan",
      "update-posisi": "/superadmin/update-posisi",
      "update-status-device": "/superadmin/update-status-device",
      "tracking-start": "/superadmin/tracking-start",
      "tracking-stop": "/superadmin/tracking-stop",
      "tracking-status": "/superadmin/tracking-status",
      "logs": "/superadmin/logs",
      "manajemen-pengguna": "/superadmin/manajemen-pengguna",
      "role-management": "/superadmin/role-management",
    };

    const path =
      menuToPath[menuId as keyof typeof menuToPath] || "/tambah-data";
    navigate(path);
  };

  return (
    <Layout activeMenu={activeMenu} onMenuClick={handleMenuClick}>
      <Routes>
        <Route path="/" element={<TambahData />} />
        <Route path="/superadmin/tambah-data" element={<TambahData />} />
        <Route
          path="/superadmin/dokumen-kepabeaan"
          element={<DokumenKepabeaan />}
        />
        <Route path="/superadmin/update-posisi" element={<UpdatePosisi />} />
        <Route
          path="/superadmin/update-status-device"
          element={<UpdateStatusDevice />}
        />
        <Route path="/superadmin/tracking-start" element={<TrackingStart />} />
        <Route path="/superadmin/tracking-stop" element={<TrackingStop />} />
        <Route
          path="/superadmin/tracking-status"
          element={<TrackingStatus />}
        />
        <Route path="/superadmin/logs" element={<Logs />} />
        <Route
          path="/superadmin/manajemen-pengguna"
          element={<ManajemenPengguna />}
        />
        <Route
          path="/superadmin/role-management"
          element={<RoleManagement />}
        />
      </Routes>
    </Layout>
  );
}

export default App;
